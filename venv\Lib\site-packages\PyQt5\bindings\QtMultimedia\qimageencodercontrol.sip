// qimageencodercontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QImageEncoderControl : QMediaControl
{
%TypeHeaderCode
#include <qimageencodercontrol.h>
%End

public:
    virtual ~QImageEncoderControl();
    virtual QStringList supportedImageCodecs() const = 0;
    virtual QString imageCodecDescription(const QString &codec) const = 0;
    virtual QList<QSize> supportedResolutions(const QImageEncoderSettings &settings, bool *continuous = 0) const = 0;
    virtual QImageEncoderSettings imageSettings() const = 0;
    virtual void setImageSettings(const QImageEncoderSettings &settings) = 0;

protected:
    explicit QImageEncoderControl(QObject *parent /TransferThis/ = 0);
};
