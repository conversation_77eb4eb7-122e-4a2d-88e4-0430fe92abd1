// qmediarecordercontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMediaRecorderControl : QMediaControl
{
%TypeHeaderCode
#include <qmediarecordercontrol.h>
%End

public:
    virtual ~QMediaRecorderControl();
    virtual QUrl outputLocation() const = 0;
    virtual bool setOutputLocation(const QUrl &location) = 0;
    virtual QMediaRecorder::State state() const = 0;
    virtual QMediaRecorder::Status status() const = 0;
    virtual qint64 duration() const = 0;
    virtual bool isMuted() const = 0;
    virtual qreal volume() const = 0;
    virtual void applySettings() = 0;

signals:
    void stateChanged(QMediaRecorder::State state);
    void statusChanged(QMediaRecorder::Status status);
    void durationChanged(qint64 position);
    void mutedChanged(bool muted);
    void volumeChanged(qreal volume);
    void actualLocationChanged(const QUrl &location);
    void error(int error, const QString &errorString);

public slots:
    virtual void setState(QMediaRecorder::State state) = 0;
    virtual void setMuted(bool muted) = 0;
    virtual void setVolume(qreal volume) = 0;

protected:
    explicit QMediaRecorderControl(QObject *parent /TransferThis/ = 0);
};
