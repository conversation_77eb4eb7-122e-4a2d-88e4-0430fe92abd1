{"gateway_config_examples": {"description": "Configuration examples for ESP32 Gateway settings", "examples": [{"name": "Set BLE scanning parameters", "description": "Configure Bluetooth scanning to not report unknown devices", "config": {"msg": "config", "seq": 12, "ble": {"rpt_unknown": 0}}, "usage": "python gateway_manager.py --mode mqtt-config --gateway-mac BC57290C8A88 --config '{\"ble\":{\"rpt_unknown\":0}}'"}, {"name": "Get gateway parameters", "description": "Retrieve current gateway configuration", "config": {"msg": "getPara", "seq": 101}, "usage": "python gateway_manager.py --mode mqtt-config --gateway-mac BC57290C8A88 --config '{\"msg\":\"getPara\",\"seq\":101}'"}, {"name": "Reboot gateway", "description": "Restart the gateway device", "config": {"msg": "reboot", "seq": 102}, "usage": "python gateway_manager.py --mode mqtt-config --gateway-mac BC57290C8A88 --config '{\"msg\":\"reboot\",\"seq\":102}'"}, {"name": "Configure WiFi settings", "description": "Set WiFi SSID and password (takes effect after restart)", "config": {"msg": "config", "seq": 103, "wifi": {"wifi_ssid": "YourWiFiNetwork", "wifi_password": "YourWiFiPassword"}}, "usage": "python gateway_manager.py --mode mqtt-config --gateway-mac BC57290C8A88 --config '{\"wifi\":{\"wifi_ssid\":\"YourWiFiNetwork\",\"wifi_password\":\"YourWiFiPassword\"}}'"}, {"name": "Set data upload protocol", "description": "Configure gateway to use HTTP protocol for data upload", "config": {"msg": "config", "seq": 104, "common": {"protocol": "http"}}, "usage": "python gateway_manager.py --mode mqtt-config --gateway-mac BC57290C8A88 --config '{\"common\":{\"protocol\":\"http\"}}'"}, {"name": "Clear configuration", "description": "Reset gateway configuration to defaults", "config": {"msg": "clrConfig", "seq": 105}, "usage": "python gateway_manager.py --mode mqtt-config --gateway-mac BC57290C8A88 --config '{\"msg\":\"clrConfig\",\"seq\":105}'"}, {"name": "Clear WiFi configuration", "description": "Remove WiFi settings from gateway", "config": {"msg": "ClrWiFi", "seq": 106}, "usage": "python gateway_manager.py --mode mqtt-config --gateway-mac BC57290C8A88 --config '{\"msg\":\"ClrWiFi\",\"seq\":106}'"}]}, "beacon_config_examples": {"description": "Configuration examples for KBeacon devices via gateway", "examples": [{"name": "Change beacon device name", "description": "Set the BLE device name of the beacon", "config": {"msg": "cfg", "name": "MyCustomBeacon"}, "usage": "python gateway_manager.py --mode beacon-config --gateway-mac BC57290C8A88 --beacon-mac BC5729040231 --config '{\"name\":\"MyCustomBeacon\"}'"}, {"name": "Set beacon transmission power", "description": "Configure the transmission power level", "config": {"msg": "cfg", "txPower": 4}, "usage": "python gateway_manager.py --mode beacon-config --gateway-mac BC57290C8A88 --beacon-mac BC5729040231 --config '{\"txPower\":4}'"}, {"name": "Configure iBeacon parameters", "description": "Set UUID, Major, and Minor values for iBeacon", "config": {"msg": "cfg", "uuid": "FDA50693-A4E2-4FB1-AFCF-C6EB07647825", "majorID": 1, "minorID": 2}, "usage": "python gateway_manager.py --mode beacon-config --gateway-mac BC57290C8A88 --beacon-mac BC5729040231 --config '{\"uuid\":\"FDA50693-A4E2-4FB1-AFCF-C6EB07647825\",\"majorID\":1,\"minorID\":2}'"}, {"name": "Set advertising interval", "description": "Configure how often the beacon advertises", "config": {"msg": "cfg", "advInterval": 1000}, "usage": "python gateway_manager.py --mode beacon-config --gateway-mac BC57290C8A88 --beacon-mac BC5729040231 --config '{\"advInterval\":1000}'"}]}, "http_config_examples": {"description": "HTTP configuration examples for local gateway management", "examples": [{"name": "Get gateway parameters via HTTP", "description": "Retrieve gateway configuration using HTTP API", "config": {"msg": "getPara", "seq": 101}, "usage": "python gateway_manager.py --mode http-config --config '{\"msg\":\"getPara\",\"seq\":101}'"}, {"name": "Reboot gateway via HTTP", "description": "Restart gateway using HTTP API", "config": {"msg": "reboot", "seq": 102}, "usage": "python gateway_manager.py --mode http-config --config '{\"msg\":\"reboot\",\"seq\":102}'"}, {"name": "Configure WiFi via HTTP", "description": "Set WiFi credentials using HTTP API", "config": {"msg": "config", "seq": 103, "wifi": {"wifi_ssid": "YourNetwork", "wifi_password": "YourPassword"}}, "usage": "python gateway_manager.py --mode http-config --config '{\"wifi\":{\"wifi_ssid\":\"YourNetwork\",\"wifi_password\":\"YourPassword\"}}'"}]}, "deployment_scenarios": {"description": "Common deployment scenarios and their configurations", "scenarios": [{"name": "Single Gateway Development", "description": "Development setup with one gateway for testing", "environment_variables": {"MQTT_BROKER": "localhost", "GATEWAY_MAC_ADDRESSES": "BC57290C8A88", "HTTP_GATEWAY_IP": "*************", "LOG_LEVEL": "DEBUG", "DATA_STORAGE_PATH": "./dev_data"}, "setup_commands": ["# Start local MQTT broker", "docker run -d -p 1883:1883 eclipse-mos<PERSON>tto", "# Configure environment", "cp .env.example .env", "# Edit .env with above variables", "# Deploy application", "python deploy.py --mode gateway"]}, {"name": "Multi-Gateway Production", "description": "Production setup with multiple gateways", "environment_variables": {"MQTT_BROKER": "prod-mqtt.company.com", "MQTT_USE_SSL": "true", "MQTT_PORT": "8883", "GATEWAY_MAC_ADDRESSES": "BC57290C8A88,BC57290C8A89,BC57290C8A90", "BEACON_MAC_ADDRESSES": "BC572908642A,DD3402075130,BC57290E567B", "LOG_LEVEL": "INFO", "LOG_FILE": "/var/log/esp32gateway/gateway.log", "DATA_STORAGE_PATH": "/opt/esp32gateway/data"}, "setup_commands": ["# Create production directories", "sudo mkdir -p /opt/esp32gateway/data", "sudo mkdir -p /var/log/esp32gateway", "# Set permissions", "sudo chown gateway:gateway /opt/esp32gateway/data", "sudo chown gateway:gateway /var/log/esp32gateway", "# Install as service", "sudo cp esp32gateway.service /etc/systemd/system/", "sudo systemctl enable esp32gateway", "sudo systemctl start esp32gateway"]}, {"name": "Cloud Deployment", "description": "Deployment in cloud environment with containerization", "environment_variables": {"MQTT_BROKER": "cloud-mqtt.amazonaws.com", "MQTT_USE_SSL": "true", "HTTP_BIND_ADDRESS": "0.0.0.0", "DATA_STORAGE_PATH": "/app/data", "LOG_LEVEL": "INFO"}, "docker_commands": ["# Build Docker image", "docker build -t esp32gateway .", "# Run container", "docker run -d --name esp32gateway -p 8080:8080 -p 8081:8081 -v /data:/app/data esp32gateway"]}, {"name": "Local Network Deployment", "description": "Deployment on local network with multiple devices", "environment_variables": {"MQTT_BROKER": "************", "HTTP_GATEWAY_IP": "*************", "HTTP_BIND_ADDRESS": "************", "GATEWAY_MAC_ADDRESSES": "BC57290C8A88,BC57290C8A89", "BEACON_MAC_ADDRESSES": "BC572908642A,DD3402075130"}, "network_setup": ["# Ensure all devices are on same network", "# Configure firewall rules", "sudo ufw allow 61613/tcp", "sudo ufw allow 8080/tcp", "sudo ufw allow 8081/tcp"]}]}, "testing_commands": {"description": "Commands for testing different aspects of the deployment", "commands": [{"name": "Test MQTT connectivity", "command": "telnet mqtt.kkmiot.com 61613", "description": "Verify MQTT broker is reachable"}, {"name": "Test HTTP gateway", "command": "curl -X POST http://*************/api -H 'Content-Type: application/json' -d '{\"msg\":\"getPara\",\"seq\":101}'", "description": "Test HTTP communication with gateway"}, {"name": "Check health status", "command": "curl http://localhost:8081/health", "description": "Verify application health"}, {"name": "Get detailed status", "command": "curl http://localhost:8081/status", "description": "Get detailed application status"}, {"name": "View metrics", "command": "curl http://localhost:8081/metrics", "description": "View application metrics"}, {"name": "Test configuration validation", "command": "python deploy.py --check-only", "description": "Validate configuration without starting application"}, {"name": "View logs", "command": "tail -f gateway.log", "description": "Monitor application logs in real-time"}]}}