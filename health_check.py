#!/usr/bin/env python3
"""
ESP32Gateway Health Check Service

This module provides a simple HTTP health check endpoint for monitoring
the ESP32Gateway application status and connectivity.
"""

import json
import time
import threading
import logging
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from typing import Dict, Any

from config import config


class HealthCheckHandler(BaseHTTPRequestHandler):
    """HTTP request handler for health check endpoints."""
    
    def do_GET(self):
        """Handle GET requests for health check."""
        if self.path == "/health":
            self._handle_health_check()
        elif self.path == "/status":
            self._handle_status_check()
        elif self.path == "/metrics":
            self._handle_metrics()
        else:
            self._send_error(404, "Not Found")
    
    def _handle_health_check(self):
        """Handle basic health check request."""
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "application": "ESP32Gateway",
            "version": "1.0.0"
        }
        
        self._send_json_response(200, health_data)
    
    def _handle_status_check(self):
        """Handle detailed status check request."""
        try:
            # Get gateway status from the health monitor
            gateway_status = self.server.health_monitor.get_gateway_status()
            mqtt_status = self.server.health_monitor.get_mqtt_status()
            
            status_data = {
                "status": "healthy" if all(gateway_status.values()) else "degraded",
                "timestamp": datetime.now().isoformat(),
                "gateways": gateway_status,
                "mqtt": mqtt_status,
                "uptime": self.server.health_monitor.get_uptime()
            }
            
            self._send_json_response(200, status_data)
            
        except Exception as e:
            error_data = {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
            self._send_json_response(500, error_data)
    
    def _handle_metrics(self):
        """Handle metrics request."""
        try:
            metrics = self.server.health_monitor.get_metrics()
            self._send_json_response(200, metrics)
            
        except Exception as e:
            error_data = {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
            self._send_json_response(500, error_data)
    
    def _send_json_response(self, status_code: int, data: Dict[str, Any]):
        """Send JSON response."""
        self.send_response(status_code)
        self.send_header("Content-Type", "application/json")
        self.send_header("Access-Control-Allow-Origin", "*")
        self.end_headers()
        
        response_data = json.dumps(data, indent=2)
        self.wfile.write(response_data.encode())
    
    def _send_error(self, status_code: int, message: str):
        """Send error response."""
        error_data = {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": message
        }
        self._send_json_response(status_code, error_data)
    
    def log_message(self, format, *args):
        """Override to use our logger instead of stderr."""
        logging.getLogger("health_check").info(format % args)


class HealthMonitor:
    """Monitors the health of gateway components."""
    
    def __init__(self):
        """Initialize health monitor."""
        self.start_time = time.time()
        self.gateway_status = {}
        self.mqtt_status = {}
        self.metrics = {
            "messages_received": 0,
            "messages_sent": 0,
            "connection_errors": 0,
            "last_activity": None
        }
        
        self.logger = logging.getLogger(__name__)
    
    def update_gateway_status(self, gateway_mac: str, connected: bool):
        """
        Update gateway connection status.
        
        Args:
            gateway_mac: Gateway MAC address
            connected: Connection status
        """
        self.gateway_status[gateway_mac] = {
            "connected": connected,
            "last_update": datetime.now().isoformat()
        }
        
        if connected:
            self.metrics["last_activity"] = datetime.now().isoformat()
    
    def update_mqtt_status(self, broker: str, connected: bool):
        """
        Update MQTT broker connection status.
        
        Args:
            broker: MQTT broker address
            connected: Connection status
        """
        self.mqtt_status[broker] = {
            "connected": connected,
            "last_update": datetime.now().isoformat()
        }
    
    def increment_metric(self, metric_name: str, value: int = 1):
        """
        Increment a metric counter.
        
        Args:
            metric_name: Name of the metric
            value: Value to increment by
        """
        if metric_name in self.metrics:
            self.metrics[metric_name] += value
        else:
            self.metrics[metric_name] = value
    
    def get_gateway_status(self) -> Dict[str, Any]:
        """Get current gateway status."""
        return {
            mac: status["connected"] 
            for mac, status in self.gateway_status.items()
        }
    
    def get_mqtt_status(self) -> Dict[str, Any]:
        """Get current MQTT status."""
        return self.mqtt_status.copy()
    
    def get_uptime(self) -> float:
        """Get application uptime in seconds."""
        return time.time() - self.start_time
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        return {
            **self.metrics,
            "uptime_seconds": self.get_uptime(),
            "gateway_count": len(self.gateway_status),
            "connected_gateways": sum(1 for status in self.gateway_status.values() if status["connected"])
        }


class HealthCheckServer:
    """HTTP server for health check endpoints."""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8081):
        """
        Initialize health check server.
        
        Args:
            host: Server host address
            port: Server port
        """
        self.host = host
        self.port = port
        self.health_monitor = HealthMonitor()
        self.server = None
        self.server_thread = None
        
        self.logger = logging.getLogger(__name__)
    
    def start(self):
        """Start the health check server."""
        try:
            self.server = HTTPServer((self.host, self.port), HealthCheckHandler)
            self.server.health_monitor = self.health_monitor
            
            self.server_thread = threading.Thread(
                target=self.server.serve_forever,
                daemon=True
            )
            self.server_thread.start()
            
            self.logger.info(f"Health check server started on {self.host}:{self.port}")
            
        except Exception as e:
            self.logger.error(f"Failed to start health check server: {e}")
            raise
    
    def stop(self):
        """Stop the health check server."""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            
            if self.server_thread:
                self.server_thread.join(timeout=5)
            
            self.logger.info("Health check server stopped")
    
    def get_monitor(self) -> HealthMonitor:
        """Get the health monitor instance."""
        return self.health_monitor


# Global health check server instance
health_server = None


def start_health_check_server():
    """Start the global health check server."""
    global health_server
    
    if health_server is None:
        port = config.get("app.health_check_port", 8081)
        health_server = HealthCheckServer(port=port)
        health_server.start()
    
    return health_server


def stop_health_check_server():
    """Stop the global health check server."""
    global health_server
    
    if health_server:
        health_server.stop()
        health_server = None


def get_health_monitor() -> HealthMonitor:
    """Get the global health monitor instance."""
    global health_server
    
    if health_server is None:
        start_health_check_server()
    
    return health_server.get_monitor()


if __name__ == "__main__":
    """Run health check server standalone for testing."""
    import sys
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        server = start_health_check_server()
        monitor = get_health_monitor()
        
        # Simulate some status updates for testing
        monitor.update_gateway_status("BC57290C8A88", True)
        monitor.update_mqtt_status("mqtt.kkmiot.com", True)
        monitor.increment_metric("messages_received", 10)
        
        print(f"Health check server running on http://localhost:8081")
        print("Available endpoints:")
        print("  GET /health   - Basic health check")
        print("  GET /status   - Detailed status")
        print("  GET /metrics  - Application metrics")
        print("\nPress Ctrl+C to stop...")
        
        # Keep running until interrupted
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nShutting down...")
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
    finally:
        stop_health_check_server()
