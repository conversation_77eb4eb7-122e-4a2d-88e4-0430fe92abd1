// qnetworkconfiguration.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkConfiguration
{
%TypeHeaderCode
#include <qnetworkconfiguration.h>
%End

public:
    QNetworkConfiguration();
    QNetworkConfiguration(const QNetworkConfiguration &other);
    ~QNetworkConfiguration();
    bool operator==(const QNetworkConfiguration &cp) const;
    bool operator!=(const QNetworkConfiguration &cp) const;

    enum Type
    {
        InternetAccessPoint,
        ServiceNetwork,
        UserChoice,
        Invalid,
    };

    enum Purpose
    {
        UnknownPurpose,
        PublicPurpose,
        PrivatePurpose,
        ServiceSpecificPurpose,
    };

    enum StateFlag
    {
        Undefined,
        Defined,
        Discovered,
        Active,
    };

    typedef QFlags<QNetworkConfiguration::StateFlag> StateFlags;

    enum BearerType
    {
        BearerUnknown,
        BearerEthernet,
        BearerWLAN,
        Bearer2G,
        BearerCDMA2000,
        BearerWCDMA,
        BearerHSPA,
        BearerBluetooth,
        BearerWiMAX,
%If (Qt_5_2_0 -)
        BearerEVDO,
%End
%If (Qt_5_2_0 -)
        BearerLTE,
%End
%If (Qt_5_2_0 -)
        Bearer3G,
%End
%If (Qt_5_2_0 -)
        Bearer4G,
%End
    };

    QNetworkConfiguration::StateFlags state() const;
    QNetworkConfiguration::Type type() const;
    QNetworkConfiguration::Purpose purpose() const;
    QNetworkConfiguration::BearerType bearerType() const;
    QString bearerTypeName() const;
%If (Qt_5_2_0 -)
    QNetworkConfiguration::BearerType bearerTypeFamily() const;
%End
    QString identifier() const;
    bool isRoamingAvailable() const;
    QList<QNetworkConfiguration> children() const;
    QString name() const;
    bool isValid() const;
    void swap(QNetworkConfiguration &other /Constrained/);
%If (Qt_5_9_0 -)
    int connectTimeout() const;
%End
%If (Qt_5_9_0 -)
    bool setConnectTimeout(int timeout);
%End
};
