#!/usr/bin/env python3
"""
ESP32Gateway Unified Manager

This module provides a unified interface for managing ESP32Gateway operations
including MQTT communication, HTTP configuration, and beacon management.
"""

import json
import time
import threading
import logging
import logging.handlers
import signal
import sys
import argparse
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

import paho.mqtt.client as mqtt
import requests

from config import config
from health_check import start_health_check_server, stop_health_check_server, get_health_monitor


class GatewayLogger:
    """Enhanced logging for gateway operations."""
    
    def __init__(self):
        self.setup_logging()
    
    def setup_logging(self):
        """Configure logging based on configuration settings."""
        log_config = config.get_logging_config()
        
        # Create logs directory if it doesn't exist
        log_file = Path(log_config["file"])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_config["level"].upper()),
            format=log_config["format"],
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.handlers.RotatingFileHandler(
                    log_file,
                    maxBytes=log_config["max_size"],
                    backupCount=log_config["backup_count"]
                )
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Gateway logging initialized")


class MQTTGatewayManager:
    """Manages MQTT communication with ESP32 gateways."""
    
    def __init__(self, gateway_mac: str):
        """
        Initialize MQTT gateway manager.
        
        Args:
            gateway_mac: Gateway MAC address
        """
        self.gateway_mac = gateway_mac
        self.mqtt_config = config.get_mqtt_config()
        self.gateway_config = config.get_gateway_config()
        self.topics = config.generate_mqtt_topics(gateway_mac)
        
        self.client = mqtt.Client()
        self.connected = False
        self.message_sequence = 0
        self.response_handlers = {}
        self.timer_handlers = {}
        
        self.logger = logging.getLogger(f"{__name__}.MQTT.{gateway_mac}")
        self._setup_mqtt_client()
    
    def _setup_mqtt_client(self):
        """Configure MQTT client callbacks and settings."""
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect
        self.client.on_publish = self._on_publish
        
        # Set authentication if provided
        if self.mqtt_config["username"] and self.mqtt_config["password"]:
            self.client.username_pw_set(
                self.mqtt_config["username"],
                self.mqtt_config["password"]
            )
        
        # Configure SSL if enabled
        if self.mqtt_config["use_ssl"]:
            self.client.tls_set()
    
    def _on_connect(self, client, userdata, flags, rc):
        """Handle MQTT connection events."""
        if rc == 0:
            self.connected = True
            self.logger.info(f"Connected to MQTT broker at {self.mqtt_config['broker']}")

            # Subscribe to gateway response topic
            client.subscribe(self.topics["sub_topic"], qos=self.mqtt_config["qos"])
            self.logger.info(f"Subscribed to topic: {self.topics['sub_topic']}")

            # Update health monitor
            health_monitor = get_health_monitor()
            health_monitor.update_mqtt_status(self.mqtt_config['broker'], True)
            health_monitor.update_gateway_status(self.gateway_mac, True)

        else:
            self.logger.error(f"Failed to connect to MQTT broker, return code: {rc}")
            # Update health monitor
            health_monitor = get_health_monitor()
            health_monitor.update_mqtt_status(self.mqtt_config['broker'], False)
            health_monitor.update_gateway_status(self.gateway_mac, False)
    
    def _on_message(self, client, userdata, msg):
        """Handle incoming MQTT messages."""
        try:
            message = json.loads(msg.payload.decode())
            self.logger.debug(f"Received message: {message}")

            # Update metrics
            health_monitor = get_health_monitor()
            health_monitor.increment_metric("messages_received")

            # Handle different message types
            msg_type = message.get("msg")
            seq = message.get("seq")

            if msg_type == "config" and seq in self.response_handlers:
                self._handle_config_response(message)
            elif msg_type == "dAck" and seq in self.response_handlers:
                self._handle_beacon_response(message)
            elif msg_type == "advData":
                self._handle_advertisement_data(message)

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode MQTT message: {e}")
            health_monitor = get_health_monitor()
            health_monitor.increment_metric("connection_errors")
        except Exception as e:
            self.logger.error(f"Error processing MQTT message: {e}")
            health_monitor = get_health_monitor()
            health_monitor.increment_metric("connection_errors")
    
    def _on_disconnect(self, client, userdata, rc):
        """Handle MQTT disconnection events."""
        self.connected = False
        if rc != 0:
            self.logger.warning("Unexpected MQTT disconnection")
        else:
            self.logger.info("MQTT client disconnected")

        # Update health monitor
        health_monitor = get_health_monitor()
        health_monitor.update_mqtt_status(self.mqtt_config['broker'], False)
        health_monitor.update_gateway_status(self.gateway_mac, False)
    
    def _on_publish(self, client, userdata, mid):
        """Handle MQTT publish events."""
        self.logger.debug(f"Message published with ID: {mid}")
    
    def _handle_config_response(self, message: Dict[str, Any]):
        """Handle gateway configuration response."""
        seq = message.get("seq")
        code = message.get("code", -1)
        
        if seq in self.response_handlers:
            handler = self.response_handlers[seq]
            
            if code == 0:
                self.logger.info(f"Gateway configuration successful: {message}")
                handler["success"] = True
            else:
                self.logger.error(f"Gateway configuration failed, code: {code}")
                handler["success"] = False
            
            handler["completed"] = True
            
            # Cancel timeout timer
            if seq in self.timer_handlers:
                self.timer_handlers[seq].cancel()
                del self.timer_handlers[seq]
    
    def _handle_beacon_response(self, message: Dict[str, Any]):
        """Handle beacon configuration response."""
        seq = message.get("seq")
        cause = message.get("cause", -1)
        
        if seq in self.response_handlers:
            handler = self.response_handlers[seq]
            
            if cause == 0:
                self.logger.info(f"Beacon command successful: {message}")
                handler["success"] = True
                handler["completed"] = True
                
                # Cancel timeout timer
                if seq in self.timer_handlers:
                    self.timer_handlers[seq].cancel()
                    del self.timer_handlers[seq]
                    
            elif cause == 1:
                self.logger.info("Gateway received command, executing...")
            else:
                self.logger.error(f"Beacon command failed, cause: {cause}")
                handler["success"] = False
                handler["completed"] = True
    
    def _handle_advertisement_data(self, message: Dict[str, Any]):
        """Handle beacon advertisement data."""
        beacon_config = config.get_beacon_config()
        beacon_macs = beacon_config["mac_addresses"]
        
        for obj in message.get("obj", []):
            dmac = obj.get("dmac", "").upper()
            
            if not beacon_macs or dmac in beacon_macs:
                self._process_beacon_data(obj)
    
    def _process_beacon_data(self, beacon_data: Dict[str, Any]):
        """Process individual beacon advertisement data."""
        mac = beacon_data.get("dmac")
        rssi = beacon_data.get("rssi")
        timestamp = beacon_data.get("time")
        beacon_type = beacon_data.get("type")
        
        self.logger.info(f"Beacon data - MAC: {mac}, RSSI: {rssi}, Type: {beacon_type}")
        
        # Store data if storage path is configured
        storage_path = config.get("app.data_storage_path")
        if storage_path:
            self._store_beacon_data(beacon_data)
    
    def _store_beacon_data(self, beacon_data: Dict[str, Any]):
        """Store beacon data to file."""
        try:
            storage_path = Path(config.get("app.data_storage_path"))
            storage_path.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d")
            filename = storage_path / f"beacon_data_{timestamp}.json"
            
            # Append data to daily file
            with open(filename, "a") as f:
                json.dump({
                    "timestamp": datetime.now().isoformat(),
                    "gateway_mac": self.gateway_mac,
                    "beacon_data": beacon_data
                }, f)
                f.write("\n")
                
        except Exception as e:
            self.logger.error(f"Failed to store beacon data: {e}")
    
    def connect(self) -> bool:
        """
        Connect to MQTT broker.
        
        Returns:
            bool: True if connection successful
        """
        try:
            self.client.connect(
                self.mqtt_config["broker"],
                self.mqtt_config["port"],
                self.mqtt_config["keepalive"]
            )
            self.client.loop_start()
            
            # Wait for connection
            timeout = 10
            while not self.connected and timeout > 0:
                time.sleep(0.1)
                timeout -= 0.1
            
            return self.connected
            
        except Exception as e:
            self.logger.error(f"Failed to connect to MQTT broker: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MQTT broker."""
        if self.connected:
            self.client.loop_stop()
            self.client.disconnect()
            self.connected = False
            self.logger.info("Disconnected from MQTT broker")
    
    def send_gateway_config(self, config_data: Dict[str, Any]) -> bool:
        """
        Send configuration command to gateway.
        
        Args:
            config_data: Configuration data to send
            
        Returns:
            bool: True if command successful
        """
        self.message_sequence += 1
        message = {
            "msg": "config",
            "seq": self.message_sequence,
            **config_data
        }
        
        return self._send_command_with_response(message)
    
    def send_beacon_config(self, beacon_mac: str, config_data: Dict[str, Any]) -> bool:
        """
        Send configuration command to beacon via gateway.
        
        Args:
            beacon_mac: Beacon MAC address
            config_data: Configuration data to send
            
        Returns:
            bool: True if command successful
        """
        beacon_config = config.get_beacon_config()
        self.message_sequence += 1
        
        message = {
            "msg": "dData",
            "mac": beacon_mac,
            "seq": self.message_sequence,
            "auth1": beacon_config["password"],
            "dType": "json",
            "data": config_data
        }
        
        return self._send_command_with_response(message)
    
    def _send_command_with_response(self, message: Dict[str, Any]) -> bool:
        """Send command and wait for response."""
        if not self.connected:
            self.logger.error("Not connected to MQTT broker")
            return False
        
        seq = message["seq"]
        
        # Setup response handler
        self.response_handlers[seq] = {
            "completed": False,
            "success": False
        }
        
        # Setup timeout timer
        def timeout_callback():
            if seq in self.response_handlers:
                self.logger.error(f"Command timeout for sequence {seq}")
                self.response_handlers[seq]["completed"] = True
                self.response_handlers[seq]["success"] = False
        
        timeout_seconds = self.gateway_config["timeout_seconds"]
        timer = threading.Timer(timeout_seconds, timeout_callback)
        self.timer_handlers[seq] = timer
        timer.start()
        
        # Send message
        try:
            json_message = json.dumps(message)
            self.logger.info(f"Sending command: {json_message}")
            
            self.client.publish(
                self.topics["pub_topic"],
                json_message,
                qos=self.mqtt_config["qos"],
                retain=self.mqtt_config["retain"]
            )
            
            # Wait for response
            while not self.response_handlers[seq]["completed"]:
                time.sleep(0.1)
            
            success = self.response_handlers[seq]["success"]
            
            # Cleanup
            del self.response_handlers[seq]
            if seq in self.timer_handlers:
                del self.timer_handlers[seq]
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to send command: {e}")
            return False


class HTTPGatewayManager:
    """Manages HTTP communication with ESP32 gateways."""
    
    def __init__(self):
        """Initialize HTTP gateway manager."""
        self.http_config = config.get_http_config()
        self.logger = logging.getLogger(f"{__name__}.HTTP")
    
    def send_config(self, config_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Send configuration to gateway via HTTP.
        
        Args:
            config_data: Configuration data to send
            
        Returns:
            Response data or None if failed
        """
        url = config.get_http_url()
        headers = {"Content-Type": "application/json"}
        
        try:
            self.logger.info(f"Sending HTTP config to {url}")
            response = requests.post(
                url,
                json=config_data,
                headers=headers,
                timeout=self.http_config["timeout"]
            )
            
            response.raise_for_status()
            result = response.json() if response.content else {}
            
            self.logger.info(f"HTTP config successful: {result}")
            return result
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP config failed: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode HTTP response: {e}")
            return None


class GatewayApplication:
    """Main gateway application that coordinates all managers."""

    def __init__(self):
        """Initialize gateway application."""
        self.logger_manager = GatewayLogger()
        self.logger = logging.getLogger(__name__)

        self.mqtt_managers = {}
        self.http_manager = HTTPGatewayManager()
        self.running = False

        # Start health check server
        self.health_server = start_health_check_server()
        self.health_monitor = get_health_monitor()

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        self.logger.info("Gateway application initialized")

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.stop()

    def start_mqtt_managers(self):
        """Start MQTT managers for all configured gateways."""
        gateway_config = config.get_gateway_config()

        for gateway_mac in gateway_config["mac_addresses"]:
            self.logger.info(f"Starting MQTT manager for gateway {gateway_mac}")

            manager = MQTTGatewayManager(gateway_mac)
            if manager.connect():
                self.mqtt_managers[gateway_mac] = manager
                self.logger.info(f"MQTT manager for {gateway_mac} started successfully")
            else:
                self.logger.error(f"Failed to start MQTT manager for {gateway_mac}")

    def stop_mqtt_managers(self):
        """Stop all MQTT managers."""
        for gateway_mac, manager in self.mqtt_managers.items():
            self.logger.info(f"Stopping MQTT manager for {gateway_mac}")
            manager.disconnect()

        self.mqtt_managers.clear()

    def run_gateway_mode(self):
        """Run in gateway mode - continuous operation."""
        self.logger.info("Starting gateway mode")
        self.running = True

        # Start MQTT managers
        self.start_mqtt_managers()

        if not self.mqtt_managers:
            self.logger.error("No MQTT managers started, exiting")
            return

        # Main loop
        try:
            while self.running:
                time.sleep(1)

                # Health check - reconnect if needed
                for gateway_mac, manager in list(self.mqtt_managers.items()):
                    if not manager.connected:
                        self.logger.warning(f"Gateway {gateway_mac} disconnected, attempting reconnect")
                        if not manager.connect():
                            self.logger.error(f"Failed to reconnect gateway {gateway_mac}")

        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
        finally:
            self.stop()

    def run_mqtt_config(self, gateway_mac: str, config_data: Dict[str, Any]):
        """Run MQTT configuration command."""
        self.logger.info(f"Running MQTT config for gateway {gateway_mac}")

        manager = MQTTGatewayManager(gateway_mac)
        if manager.connect():
            success = manager.send_gateway_config(config_data)
            manager.disconnect()

            if success:
                self.logger.info("MQTT configuration completed successfully")
            else:
                self.logger.error("MQTT configuration failed")

            return success
        else:
            self.logger.error("Failed to connect to MQTT broker")
            return False

    def run_beacon_config(self, gateway_mac: str, beacon_mac: str, config_data: Dict[str, Any]):
        """Run beacon configuration command."""
        self.logger.info(f"Running beacon config for {beacon_mac} via gateway {gateway_mac}")

        manager = MQTTGatewayManager(gateway_mac)
        if manager.connect():
            success = manager.send_beacon_config(beacon_mac, config_data)
            manager.disconnect()

            if success:
                self.logger.info("Beacon configuration completed successfully")
            else:
                self.logger.error("Beacon configuration failed")

            return success
        else:
            self.logger.error("Failed to connect to MQTT broker")
            return False

    def run_http_config(self, config_data: Dict[str, Any]):
        """Run HTTP configuration command."""
        self.logger.info("Running HTTP configuration")

        result = self.http_manager.send_config(config_data)
        if result:
            self.logger.info("HTTP configuration completed successfully")
            return True
        else:
            self.logger.error("HTTP configuration failed")
            return False

    def stop(self):
        """Stop the gateway application."""
        self.logger.info("Stopping gateway application")
        self.running = False
        self.stop_mqtt_managers()

        # Stop health check server
        stop_health_check_server()


def main():
    """Main entry point with command-line interface."""
    parser = argparse.ArgumentParser(
        description="ESP32Gateway Unified Manager",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run in continuous gateway mode
  python gateway_manager.py --mode gateway

  # Send MQTT configuration to gateway
  python gateway_manager.py --mode mqtt-config --gateway-mac BC57290C8A88 --config '{"ble":{"rpt_unknown":0}}'

  # Configure beacon via MQTT
  python gateway_manager.py --mode beacon-config --gateway-mac BC57290C8A88 --beacon-mac BC5729040231 --config '{"name":"MyBeacon"}'

  # Send HTTP configuration
  python gateway_manager.py --mode http-config --config '{"msg":"getPara","seq":101}'
        """
    )

    parser.add_argument(
        "--mode",
        choices=["gateway", "mqtt-config", "beacon-config", "http-config"],
        default="gateway",
        help="Operation mode (default: gateway)"
    )

    parser.add_argument(
        "--gateway-mac",
        help="Gateway MAC address (required for MQTT operations)"
    )

    parser.add_argument(
        "--beacon-mac",
        help="Beacon MAC address (required for beacon-config mode)"
    )

    parser.add_argument(
        "--config",
        help="Configuration data as JSON string"
    )

    parser.add_argument(
        "--config-file",
        help="Configuration data from JSON file"
    )

    args = parser.parse_args()

    # Initialize application
    app = GatewayApplication()

    try:
        if args.mode == "gateway":
            app.run_gateway_mode()

        elif args.mode == "mqtt-config":
            if not args.gateway_mac:
                parser.error("--gateway-mac is required for mqtt-config mode")

            config_data = {}
            if args.config:
                config_data = json.loads(args.config)
            elif args.config_file:
                with open(args.config_file, 'r') as f:
                    config_data = json.load(f)
            else:
                parser.error("Either --config or --config-file is required")

            success = app.run_mqtt_config(args.gateway_mac, config_data)
            sys.exit(0 if success else 1)

        elif args.mode == "beacon-config":
            if not args.gateway_mac:
                parser.error("--gateway-mac is required for beacon-config mode")
            if not args.beacon_mac:
                parser.error("--beacon-mac is required for beacon-config mode")

            config_data = {}
            if args.config:
                config_data = json.loads(args.config)
            elif args.config_file:
                with open(args.config_file, 'r') as f:
                    config_data = json.load(f)
            else:
                parser.error("Either --config or --config-file is required")

            success = app.run_beacon_config(args.gateway_mac, args.beacon_mac, config_data)
            sys.exit(0 if success else 1)

        elif args.mode == "http-config":
            config_data = {}
            if args.config:
                config_data = json.loads(args.config)
            elif args.config_file:
                with open(args.config_file, 'r') as f:
                    config_data = json.load(f)
            else:
                parser.error("Either --config or --config-file is required")

            success = app.run_http_config(config_data)
            sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        app.logger.info("Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        app.logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
