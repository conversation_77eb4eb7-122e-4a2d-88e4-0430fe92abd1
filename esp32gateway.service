[Unit]
Description=ESP32Gateway Service
Documentation=https://github.com/kkmhogen/ESP32Gateway
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=gateway
Group=gateway
WorkingDirectory=/opt/esp32gateway
Environment=PATH=/opt/esp32gateway/venv/bin
ExecStart=/opt/esp32gateway/venv/bin/python /opt/esp32gateway/deploy.py --mode gateway
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=esp32gateway

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/esp32gateway/data /var/log/esp32gateway

# Resource limits
LimitNOFILE=65536
MemoryMax=512M

# Environment variables (can be overridden in /etc/systemd/system/esp32gateway.service.d/override.conf)
Environment=LOG_LEVEL=INFO
Environment=LOG_FILE=/var/log/esp32gateway/gateway.log
Environment=DATA_STORAGE_PATH=/opt/esp32gateway/data

[Install]
WantedBy=multi-user.target
