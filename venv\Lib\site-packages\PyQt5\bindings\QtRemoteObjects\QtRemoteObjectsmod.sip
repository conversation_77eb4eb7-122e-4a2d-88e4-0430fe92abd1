// QtRemoteObjectsmod.sip generated by MetaSIP
//
// This file is part of the QtRemoteObjects Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt5.QtRemoteObjects, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip

%Copying
Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt5.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype sip.simplewrapper

%Include qremoteobjectabstractitemmodelreplica.sip
%Include qremoteobjectdynamicreplica.sip
%Include qremoteobjectnode.sip
%Include qremoteobjectregistry.sip
%Include qremoteobjectreplica.sip
%Include qtremoteobjectglobal.sip
