// qgeopositioninfosource.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QGeoPositionInfoSource : QObject
{
%TypeHeaderCode
#include <qgeopositioninfosource.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QGeoPositionInfoSource, &sipType_QGeoPositionInfoSource, 3, 1},
        {sipName_QGeoSatelliteInfoSource, &sipType_QGeoSatelliteInfoSource, -1, 2},
        {sipName_QGeoAreaMonitorSource, &sipType_QGeoAreaMonitorSource, -1, -1},
        {sipName_QNmeaPositionInfoSource, &sipType_QNmeaPositionInfoSource, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum Error
    {
        AccessError,
        ClosedError,
        UnknownSourceError,
        NoError,
    };

    enum PositioningMethod
    {
        NoPositioningMethods,
        SatellitePositioningMethods,
        NonSatellitePositioningMethods,
        AllPositioningMethods,
    };

    typedef QFlags<QGeoPositionInfoSource::PositioningMethod> PositioningMethods;
    explicit QGeoPositionInfoSource(QObject *parent /TransferThis/);
    virtual ~QGeoPositionInfoSource();
    virtual void setUpdateInterval(int msec);
    int updateInterval() const;
    virtual void setPreferredPositioningMethods(QGeoPositionInfoSource::PositioningMethods methods);
    QGeoPositionInfoSource::PositioningMethods preferredPositioningMethods() const;
    virtual QGeoPositionInfo lastKnownPosition(bool fromSatellitePositioningMethodsOnly = false) const = 0;
    virtual QGeoPositionInfoSource::PositioningMethods supportedPositioningMethods() const = 0;
    virtual int minimumUpdateInterval() const = 0;
    QString sourceName() const;
    static QGeoPositionInfoSource *createDefaultSource(QObject *parent /TransferThis/) /Factory/;
%If (Qt_5_14_0 -)
    static QGeoPositionInfoSource *createDefaultSource(const QVariantMap &parameters, QObject *parent /TransferThis/) /Factory/;
%End
    static QGeoPositionInfoSource *createSource(const QString &sourceName, QObject *parent /TransferThis/) /Factory/;
%If (Qt_5_14_0 -)
    static QGeoPositionInfoSource *createSource(const QString &sourceName, const QVariantMap &parameters, QObject *parent /TransferThis/) /Factory/;
%End
    static QStringList availableSources();
    virtual QGeoPositionInfoSource::Error error() const = 0;

public slots:
    virtual void startUpdates() = 0;
    virtual void stopUpdates() = 0;
    virtual void requestUpdate(int timeout = 0) = 0;

signals:
    void positionUpdated(const QGeoPositionInfo &update);
    void updateTimeout();
    void error(QGeoPositionInfoSource::Error);
%If (Qt_5_12_0 -)
    void supportedPositioningMethodsChanged();
%End

public:
%If (Qt_5_14_0 -)
    bool setBackendProperty(const QString &name, const QVariant &value);
%End
%If (Qt_5_14_0 -)
    QVariant backendProperty(const QString &name) const;
%End

private:
    QGeoPositionInfoSource(const QGeoPositionInfoSource &);
};

%End
%If (Qt_5_2_0 -)
QFlags<QGeoPositionInfoSource::PositioningMethod> operator|(QGeoPositionInfoSource::PositioningMethod f1, QFlags<QGeoPositionInfoSource::PositioningMethod> f2);
%End
