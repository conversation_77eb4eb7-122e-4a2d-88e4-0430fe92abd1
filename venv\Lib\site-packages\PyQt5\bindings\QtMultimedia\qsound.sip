// qsound.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSound : QObject
{
%TypeHeaderCode
#include <qsound.h>
%End

public:
    enum Loop
    {
        Infinite,
    };

    QSound(const QString &filename, QObject *parent /TransferThis/ = 0);
    virtual ~QSound();
    static void play(const QString &filename);
    int loops() const;
    int loopsRemaining() const;
    void setLoops(int);
    QString fileName() const;
    bool isFinished() const;

public slots:
    void play();
    void stop();
};
