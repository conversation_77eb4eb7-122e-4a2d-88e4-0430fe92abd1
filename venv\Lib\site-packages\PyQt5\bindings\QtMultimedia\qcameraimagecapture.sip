// qcameraimagecapture.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraImageCapture : QObject, QMediaBindableInterface
{
%TypeHeaderCode
#include <qcameraimagecapture.h>
%End

public:
    enum Error
    {
        NoError,
        NotReadyError,
        ResourceError,
        OutOfSpaceError,
        NotSupportedFeatureError,
        FormatError,
    };

    enum DriveMode
    {
        SingleImageCapture,
    };

    enum CaptureDestination
    {
        CaptureToFile,
        CaptureToBuffer,
    };

    typedef QFlags<QCameraImageCapture::CaptureDestination> CaptureDestinations;
    QCameraImageCapture(QMediaObject *mediaObject, QObject *parent /TransferThis/ = 0);
    virtual ~QCameraImageCapture();
    bool isAvailable() const;
    QMultimedia::AvailabilityStatus availability() const;
    virtual QMediaObject *mediaObject() const;
    QCameraImageCapture::Error error() const;
    QString errorString() const;
    bool isReadyForCapture() const;
    QStringList supportedImageCodecs() const;
    QString imageCodecDescription(const QString &codecName) const;
    QList<QSize> supportedResolutions(const QImageEncoderSettings &settings = QImageEncoderSettings(), bool *continuous = 0) const;
    QImageEncoderSettings encodingSettings() const;
    void setEncodingSettings(const QImageEncoderSettings &settings);
    QList<QVideoFrame::PixelFormat> supportedBufferFormats() const;
    QVideoFrame::PixelFormat bufferFormat() const;
    void setBufferFormat(const QVideoFrame::PixelFormat format);
    bool isCaptureDestinationSupported(QCameraImageCapture::CaptureDestinations destination) const;
    QCameraImageCapture::CaptureDestinations captureDestination() const;
    void setCaptureDestination(QCameraImageCapture::CaptureDestinations destination);

public slots:
    int capture(const QString &file = QString()) /ReleaseGIL/;
    void cancelCapture();

signals:
    void error(int id, QCameraImageCapture::Error error, const QString &errorString);
    void readyForCaptureChanged(bool);
    void bufferFormatChanged(QVideoFrame::PixelFormat);
    void captureDestinationChanged(QCameraImageCapture::CaptureDestinations);
    void imageExposed(int id);
    void imageCaptured(int id, const QImage &preview);
    void imageMetadataAvailable(int id, const QString &key, const QVariant &value);
    void imageAvailable(int id, const QVideoFrame &image);
    void imageSaved(int id, const QString &fileName);

protected:
    virtual bool setMediaObject(QMediaObject *);
};

QFlags<QCameraImageCapture::CaptureDestination> operator|(QCameraImageCapture::CaptureDestination f1, QFlags<QCameraImageCapture::CaptureDestination> f2);
