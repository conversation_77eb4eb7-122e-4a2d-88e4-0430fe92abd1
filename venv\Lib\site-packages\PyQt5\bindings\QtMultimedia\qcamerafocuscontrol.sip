// qcamerafocuscontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraFocusControl : QMediaControl
{
%TypeHeaderCode
#include <qcamerafocuscontrol.h>
%End

public:
    virtual ~QCameraFocusControl();
    virtual QCameraFocus::FocusModes focusMode() const = 0;
    virtual void setFocusMode(QCameraFocus::FocusModes mode) = 0;
    virtual bool isFocusModeSupported(QCameraFocus::FocusModes mode) const = 0;
    virtual QCameraFocus::FocusPointMode focusPointMode() const = 0;
    virtual void setFocusPointMode(QCameraFocus::FocusPointMode mode) = 0;
    virtual bool isFocusPointModeSupported(QCameraFocus::FocusPointMode mode) const = 0;
    virtual QPointF customFocusPoint() const = 0;
    virtual void setCustomFocusPoint(const QPointF &point) = 0;
    virtual QCameraFocusZoneList focusZones() const = 0;

signals:
    void focusModeChanged(QCameraFocus::FocusModes mode);
    void focusPointModeChanged(QCameraFocus::FocusPointMode mode);
    void customFocusPointChanged(const QPointF &point);
    void focusZonesChanged();

protected:
    explicit QCameraFocusControl(QObject *parent /TransferThis/ = 0);
};
