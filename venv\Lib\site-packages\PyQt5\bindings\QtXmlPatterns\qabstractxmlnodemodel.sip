// qabstractxmlnodemodel.sip generated by MetaSIP
//
// This file is part of the QtXmlPatterns Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXmlNodeModelIndex
{
%TypeHeaderCode
#include <qabstractxmlnodemodel.h>
%End

public:
    QXmlNodeModelIndex();
    QXmlNodeModelIndex(const QXmlNodeModelIndex &other);
    bool operator==(const QXmlNodeModelIndex &other) const;
    bool operator!=(const QXmlNodeModelIndex &other) const;

    enum NodeKind
    {
        Attribute,
        Comment,
        Document,
        Element,
        Namespace,
        ProcessingInstruction,
        Text,
    };

    enum DocumentOrder
    {
        Precedes,
        Is,
        Follows,
    };

    qint64 data() const;
    SIP_PYOBJECT internalPointer() const;
%MethodCode
        sipRes = reinterpret_cast<PyObject *>(sipCpp->internalPointer());
        
        if (!sipRes)
            sipRes = Py_None;
        
        Py_INCREF(sipRes);
%End

    const QAbstractXmlNodeModel *model() const;
    qint64 additionalData() const;
    bool isNull() const;
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

class QAbstractXmlNodeModel
{
%TypeHeaderCode
#include <qabstractxmlnodemodel.h>
%End

public:
    enum SimpleAxis
    {
        Parent,
        FirstChild,
        PreviousSibling,
        NextSibling,
    };

    QAbstractXmlNodeModel();
    virtual ~QAbstractXmlNodeModel();
    virtual QUrl baseUri(const QXmlNodeModelIndex &ni) const = 0;
    virtual QUrl documentUri(const QXmlNodeModelIndex &ni) const = 0;
    virtual QXmlNodeModelIndex::NodeKind kind(const QXmlNodeModelIndex &ni) const = 0;
    virtual QXmlNodeModelIndex::DocumentOrder compareOrder(const QXmlNodeModelIndex &ni1, const QXmlNodeModelIndex &ni2) const = 0;
    virtual QXmlNodeModelIndex root(const QXmlNodeModelIndex &n) const = 0;
    virtual QXmlName name(const QXmlNodeModelIndex &ni) const = 0;
    virtual QString stringValue(const QXmlNodeModelIndex &n) const = 0;
    virtual QVariant typedValue(const QXmlNodeModelIndex &n) const = 0;
    virtual QVector<QXmlName> namespaceBindings(const QXmlNodeModelIndex &n) const = 0;
    virtual QXmlNodeModelIndex elementById(const QXmlName &NCName) const = 0;
    virtual QVector<QXmlNodeModelIndex> nodesByIdref(const QXmlName &NCName) const = 0;
    QSourceLocation sourceLocation(const QXmlNodeModelIndex &index) const;

protected:
    virtual QXmlNodeModelIndex nextFromSimpleAxis(QAbstractXmlNodeModel::SimpleAxis axis, const QXmlNodeModelIndex &origin) const = 0;
    virtual QVector<QXmlNodeModelIndex> attributes(const QXmlNodeModelIndex &element) const = 0;
    QXmlNodeModelIndex createIndex(qint64 data) const;
    QXmlNodeModelIndex createIndex(qint64 data, qint64 additionalData) const;
    QXmlNodeModelIndex createIndex(SIP_PYOBJECT pointer, qint64 additionalData = 0) const [QXmlNodeModelIndex (void *pointer, qint64 additionalData = 0)];

private:
    QAbstractXmlNodeModel(const QAbstractXmlNodeModel &);
};

class QXmlItem
{
%TypeHeaderCode
#include <qabstractxmlnodemodel.h>
%End

public:
    QXmlItem();
    QXmlItem(const QXmlItem &other);
    QXmlItem(const QXmlNodeModelIndex &node);
    QXmlItem(const QVariant &atomicValue);
    ~QXmlItem();
    bool isNull() const;
    bool isNode() const;
    bool isAtomicValue() const;
    QVariant toAtomicValue() const;
    QXmlNodeModelIndex toNodeModelIndex() const;
};
