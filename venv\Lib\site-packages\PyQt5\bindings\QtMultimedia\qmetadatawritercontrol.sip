// qmetadatawritercontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMetaDataWriterControl : QMediaControl
{
%TypeHeaderCode
#include <qmetadatawritercontrol.h>
%End

public:
    virtual ~QMetaDataWriterControl();
    virtual bool isWritable() const = 0;
    virtual bool isMetaDataAvailable() const = 0;
    virtual QVariant metaData(const QString &key) const = 0;
    virtual void setMetaData(const QString &key, const QVariant &value) = 0;
    virtual QStringList availableMetaData() const = 0;

signals:
    void metaDataChanged();
    void metaDataChanged(const QString &key, const QVariant &value);
    void writableChanged(bool writable);
    void metaDataAvailableChanged(bool available);

protected:
    explicit QMetaDataWriterControl(QObject *parent /TransferThis/ = 0);
};
