version: '3.8'

services:
  # ESP32Gateway Application
  esp32gateway:
    build: .
    container_name: esp32gateway
    restart: unless-stopped
    ports:
      - "8080:8080"  # HTTP server port
      - "8081:8081"  # Health check port
    volumes:
      - gateway_data:/app/data
      - gateway_logs:/app/logs
      - ./config:/app/config:ro  # Mount config directory as read-only
    environment:
      # MQTT Configuration
      - MQTT_BROKER=${MQTT_BROKER:-mqtt.kkmiot.com}
      - MQTT_PORT=${MQTT_PORT:-61613}
      - MQTT_USERNAME=${MQTT_USERNAME:-kkmtest}
      - MQTT_PASSWORD=${MQTT_PASSWORD:-testpassword}
      - MQTT_USE_SSL=${MQTT_USE_SSL:-false}
      
      # Gateway Configuration
      - GATEWAY_MAC_ADDRESSES=${GATEWAY_MAC_ADDRESSES:-BC57290C8A88}
      - GATEWAY_TIMEOUT_SECONDS=${GATEWAY_TIMEOUT_SECONDS:-30}
      
      # HTTP Configuration
      - HTTP_GATEWAY_IP=${HTTP_GATEWAY_IP:-*************}
      - HTTP_GATEWAY_PORT=${HTTP_GATEWAY_PORT:-80}
      - HTTP_BIND_ADDRESS=0.0.0.0
      - HTTP_LISTEN_PORT=8080
      
      # Beacon Configuration
      - BEACON_MAC_ADDRESSES=${BEACON_MAC_ADDRESSES:-BC572908642A,DD3402075130}
      - BEACON_PASSWORD=${BEACON_PASSWORD:-0000000000000000}
      
      # Logging Configuration
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE=/app/logs/gateway.log
      
      # Application Configuration
      - APP_MODE=gateway
      - DATA_STORAGE_PATH=/app/data
      - HEALTH_CHECK_PORT=8081
    networks:
      - gateway_network
    depends_on:
      - mosquitto
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Local MQTT Broker (optional - for development/testing)
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: mosquitto
    restart: unless-stopped
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - mosquitto_data:/mosquitto/data
      - mosquitto_logs:/mosquitto/log
      - ./mosquitto/config:/mosquitto/config:ro
    networks:
      - gateway_network
    command: mosquitto -c /mosquitto/config/mosquitto.conf

  # Grafana for monitoring (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    networks:
      - gateway_network
    profiles:
      - monitoring

  # Prometheus for metrics collection (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - gateway_network
    profiles:
      - monitoring

volumes:
  gateway_data:
    driver: local
  gateway_logs:
    driver: local
  mosquitto_data:
    driver: local
  mosquitto_logs:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  gateway_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
