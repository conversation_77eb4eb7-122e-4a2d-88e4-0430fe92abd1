# ESP32Gateway Docker Image
# Multi-stage build for optimized production image

# Build stage
FROM python:3.9-slim as builder

# Set working directory
WORKDIR /app

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.9-slim

# Create non-root user for security
RUN groupadd -r gateway && useradd -r -g gateway gateway

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy Python packages from builder stage
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application files
COPY --chown=gateway:gateway . .

# Create necessary directories
RUN mkdir -p /app/data /app/logs && \
    chown -R gateway:gateway /app/data /app/logs

# Set environment variables
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV LOG_LEVEL=INFO
ENV DATA_STORAGE_PATH=/app/data
ENV LOG_FILE=/app/logs/gateway.log

# Expose ports
EXPOSE 8080 8081

# Switch to non-root user
USER gateway

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8081/health || exit 1

# Default command
CMD ["python", "deploy.py", "--mode", "gateway"]

# Labels for metadata
LABEL maintainer="ESP32Gateway Team"
LABEL version="1.0.0"
LABEL description="ESP32Gateway application for IoT device management"
