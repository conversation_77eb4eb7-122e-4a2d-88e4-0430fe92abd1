# ESP32Gateway Deployment Guide

This comprehensive guide explains how to deploy and configure the ESP32Gateway application for production use on gateway devices.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Quick Start](#quick-start)
4. [Detailed Setup](#detailed-setup)
5. [Configuration](#configuration)
6. [Gateway Data Processing](#gateway-data-processing)
7. [Network Configuration](#network-configuration)
8. [Deployment Environments](#deployment-environments)
9. [Troubleshooting](#troubleshooting)
10. [Monitoring and Maintenance](#monitoring-and-maintenance)

## Overview

The ESP32Gateway application provides a unified interface for managing ESP32-based IoT gateways that collect data from Bluetooth beacons and forward it via MQTT or HTTP protocols. This deployment solution makes the application network-agnostic and suitable for various deployment environments.

### Key Features

- **Network-Agnostic Design**: No hardcoded IP addresses or network settings
- **Multi-Gateway Support**: Handle multiple ESP32 gateways simultaneously
- **Flexible Configuration**: Environment variables and configuration files
- **Multiple Protocols**: MQTT and HTTP communication support
- **Production Ready**: Logging, monitoring, and error handling
- **Cross-Platform**: Windows, Linux, and macOS support

## Prerequisites

### System Requirements

- **Python**: Version 3.8 or higher
- **Operating System**: Windows 10+, Linux (Ubuntu 18.04+), or macOS 10.14+
- **Memory**: Minimum 512MB RAM available
- **Storage**: 100MB free disk space
- **Network**: Internet connectivity for MQTT broker communication

### Network Requirements

- **MQTT Broker**: Access to MQTT broker (default: mqtt.kkmiot.com:61613)
- **Gateway Network**: ESP32 gateways must be on accessible network
- **Firewall**: Ensure required ports are open (see Network Configuration)

## Quick Start

### Windows

1. **Download and extract** the ESP32Gateway project files
2. **Run the deployment script**:
   ```cmd
   deploy.bat
   ```
3. **Follow the interactive prompts** to configure and start the gateway

### Linux/macOS

1. **Make the setup script executable**:
   ```bash
   chmod +x setup_environment.py
   ```
2. **Run the deployment**:
   ```bash
   python3 deploy.py
   ```

## Detailed Setup

### Step 1: Environment Setup

The deployment process automatically creates a Python virtual environment and installs all required dependencies.

**Manual Setup (if needed):**

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### Step 2: Configuration

1. **Copy the example configuration**:
   ```bash
   cp .env.example .env
   ```

2. **Edit the .env file** with your specific settings:
   ```bash
   # Basic MQTT Configuration
   MQTT_BROKER=your-mqtt-broker.com
   MQTT_USERNAME=your-username
   MQTT_PASSWORD=your-password
   
   # Gateway MAC Addresses (comma-separated)
   GATEWAY_MAC_ADDRESSES=BC57290C8A88,BC57290C8A89
   
   # HTTP Gateway Configuration
   HTTP_GATEWAY_IP=*************
   ```

### Step 3: Validation

Run the configuration validator:
```bash
python deploy.py --check-only
```

### Step 4: Deployment

Start the gateway application:
```bash
python deploy.py --mode gateway
```

## Configuration

### Environment Variables

The application uses environment variables for all configuration. Key settings include:

#### MQTT Configuration
```bash
MQTT_BROKER=mqtt.kkmiot.com          # MQTT broker hostname
MQTT_PORT=61613                      # MQTT broker port
MQTT_USERNAME=kkmtest                # MQTT username
MQTT_PASSWORD=testpassword           # MQTT password
MQTT_USE_SSL=false                   # Enable SSL/TLS
```

#### Gateway Configuration
```bash
GATEWAY_MAC_ADDRESSES=BC57290C8A88   # Comma-separated MAC addresses
GATEWAY_TIMEOUT_SECONDS=30           # Command timeout
GATEWAY_RETRY_ATTEMPTS=3             # Retry attempts
```

#### HTTP Configuration
```bash
HTTP_GATEWAY_IP=*************        # Gateway IP address
HTTP_GATEWAY_PORT=80                 # Gateway HTTP port
HTTP_BIND_ADDRESS=0.0.0.0            # Local bind address
```

#### Beacon Configuration
```bash
BEACON_MAC_ADDRESSES=BC572908642A,DD3402075130  # Monitored beacons
BEACON_PASSWORD=0000000000000000     # Beacon connection password
```

### Configuration Validation

The application validates all configuration on startup:

- **Network connectivity** to MQTT broker
- **MAC address format** validation
- **Port range** validation (1-65535)
- **Required settings** presence check

## Gateway Data Processing

### Data Flow Architecture

```
Bluetooth Beacons → ESP32 Gateways → MQTT/HTTP → Gateway Application → Data Storage
```

### How the Gateway Receives Data

1. **Bluetooth Scanning**: ESP32 gateways continuously scan for Bluetooth advertisements
2. **Data Filtering**: Gateways filter data based on configured beacon MAC addresses
3. **Protocol Transmission**: Data is sent via MQTT or HTTP to the gateway application
4. **Data Processing**: Application processes, logs, and stores the received data

### Multi-Device Support

The application supports multiple devices simultaneously:

- **Multiple ESP32 Gateways**: Each with unique MAC addresses
- **Multiple Bluetooth Beacons**: Configurable beacon filtering
- **Concurrent Connections**: Parallel MQTT connections for each gateway
- **Load Balancing**: Automatic distribution across available gateways

### Data Storage

Received data is automatically stored in JSON format:

```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "gateway_mac": "BC57290C8A88",
  "beacon_data": {
    "dmac": "BC572908642A",
    "rssi": -65,
    "type": 4,
    "uuid": "FDA50693-A4E2-4FB1-AFCF-C6EB07647825",
    "majorID": 1,
    "minorID": 2
  }
}
```

## Network Configuration

### MQTT Network Setup

**Required Ports:**
- **1883**: Standard MQTT (non-encrypted)
- **8883**: MQTT over SSL/TLS
- **61613**: Custom MQTT port (default for this application)

**Firewall Configuration:**
```bash
# Allow outbound MQTT connections
iptables -A OUTPUT -p tcp --dport 61613 -j ACCEPT
iptables -A OUTPUT -p tcp --dport 8883 -j ACCEPT
```

### HTTP Network Setup

**Required Ports:**
- **80**: HTTP communication with gateways
- **8080**: Local HTTP server (configurable)
- **8081**: Health check endpoint

**Gateway Network Requirements:**
- ESP32 gateways must be on the same network or routable network
- Gateway IP addresses must be accessible from the application host
- No NAT restrictions for bidirectional communication

### Network Interface Binding

The application binds to `0.0.0.0` by default, making it accessible on all network interfaces:

```bash
# Configure specific interface binding
HTTP_BIND_ADDRESS=************      # Bind to specific IP
HTTP_LISTEN_PORT=8080                # Custom listening port
```

### SSL/TLS Configuration

For secure MQTT communication:

```bash
MQTT_USE_SSL=true
MQTT_PORT=8883
```

**Certificate Setup** (if using custom certificates):
1. Place certificates in `./certs/` directory
2. Configure certificate paths in environment variables
3. Ensure proper certificate validation

## Deployment Environments

### Development Environment

```bash
# .env configuration for development
MQTT_BROKER=localhost
HTTP_GATEWAY_IP=127.0.0.1
LOG_LEVEL=DEBUG
DATA_STORAGE_PATH=./dev_data
```

**Setup:**
```bash
# Start local MQTT broker (using Docker)
docker run -it -p 1883:1883 eclipse-mosquitto

# Configure for local development
cp .env.example .env
# Edit .env with local settings
python deploy.py --mode gateway
```

### Production Environment

```bash
# .env configuration for production
MQTT_BROKER=prod-mqtt.company.com
MQTT_USE_SSL=true
MQTT_PORT=8883
LOG_LEVEL=INFO
LOG_FILE=/var/log/esp32gateway/gateway.log
DATA_STORAGE_PATH=/opt/esp32gateway/data
```

**Setup:**
```bash
# Create production directories
sudo mkdir -p /opt/esp32gateway/data
sudo mkdir -p /var/log/esp32gateway

# Set permissions
sudo chown gateway:gateway /opt/esp32gateway/data
sudo chown gateway:gateway /var/log/esp32gateway

# Deploy as service (systemd example)
sudo cp esp32gateway.service /etc/systemd/system/
sudo systemctl enable esp32gateway
sudo systemctl start esp32gateway
```

### Cloud Deployment

**Docker Deployment:**

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080 8081

CMD ["python", "deploy.py", "--mode", "gateway"]
```

**Kubernetes Deployment:**

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: esp32gateway
spec:
  replicas: 1
  selector:
    matchLabels:
      app: esp32gateway
  template:
    metadata:
      labels:
        app: esp32gateway
    spec:
      containers:
      - name: esp32gateway
        image: esp32gateway:latest
        ports:
        - containerPort: 8080
        - containerPort: 8081
        env:
        - name: MQTT_BROKER
          value: "mqtt.cluster.local"
        - name: LOG_LEVEL
          value: "INFO"
```

### Local Network Deployment

For deployment on local networks with multiple gateways:

```bash
# Configure for local network
MQTT_BROKER=************
HTTP_GATEWAY_IP=*************,************1,************2
HTTP_BIND_ADDRESS=************
GATEWAY_MAC_ADDRESSES=BC57290C8A88,BC57290C8A89,BC57290C8A90
```

## Troubleshooting

### Common Issues

#### 1. MQTT Connection Failed

**Symptoms:**
```
ERROR: Failed to connect to MQTT broker
```

**Solutions:**
- Verify MQTT broker hostname and port
- Check network connectivity: `telnet mqtt-broker.com 61613`
- Verify credentials (username/password)
- Check firewall settings

#### 2. Gateway Not Responding

**Symptoms:**
```
WARNING: Gateway BC57290C8A88 disconnected
```

**Solutions:**
- Verify gateway MAC address configuration
- Check gateway network connectivity
- Ensure gateway is powered and operational
- Verify MQTT topic configuration

#### 3. Configuration Validation Failed

**Symptoms:**
```
ERROR: Configuration validation failed
```

**Solutions:**
- Check .env file syntax
- Verify all required variables are set
- Validate MAC address format (no colons)
- Check port ranges (1-65535)

#### 4. Permission Denied Errors

**Symptoms:**
```
PermissionError: [Errno 13] Permission denied
```

**Solutions:**
- Check file permissions: `chmod 755 deploy.py`
- Verify directory write permissions
- Run with appropriate user privileges
- Check log file directory permissions

### Debugging

#### Enable Debug Logging

```bash
LOG_LEVEL=DEBUG
python deploy.py --mode gateway
```

#### Network Debugging

```bash
# Test MQTT connectivity
telnet mqtt.kkmiot.com 61613

# Test HTTP connectivity
curl -X POST http://*************/api -H "Content-Type: application/json" -d '{"msg":"getPara","seq":101}'

# Check listening ports
netstat -tlnp | grep python
```

#### Configuration Debugging

```bash
# Validate configuration
python -c "from config import config; print(config.to_dict())"

# Test specific configuration
python deploy.py --check-only
```

### Log Analysis

**Log Locations:**
- **Development**: `./gateway.log`
- **Production**: `/var/log/esp32gateway/gateway.log`
- **Docker**: Container logs via `docker logs`

**Key Log Messages:**
```bash
# Successful startup
INFO - Gateway application initialized
INFO - Connected to MQTT broker at mqtt.kkmiot.com

# Configuration issues
ERROR - MQTT broker not configured
WARNING - Network connectivity test failed

# Runtime issues
ERROR - Failed to connect to MQTT broker
WARNING - Gateway BC57290C8A88 disconnected
```

## Monitoring and Maintenance

### Health Checks

The application provides a health check endpoint:

```bash
# Check application health
curl http://localhost:8081/health

# Expected response
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "gateways": {
    "BC57290C8A88": "connected",
    "BC57290C8A89": "connected"
  }
}
```

### Performance Monitoring

**Key Metrics to Monitor:**
- MQTT connection status
- Message processing rate
- Error rates
- Memory usage
- Disk space (for data storage)

**Monitoring Commands:**
```bash
# Check process status
ps aux | grep python

# Monitor resource usage
top -p $(pgrep -f gateway_manager)

# Check disk usage
df -h /opt/esp32gateway/data

# Monitor log file size
ls -lh /var/log/esp32gateway/
```

### Maintenance Tasks

#### Log Rotation

Logs are automatically rotated based on configuration:

```bash
LOG_MAX_SIZE=10485760    # 10MB
LOG_BACKUP_COUNT=5       # Keep 5 backup files
```

#### Data Cleanup

```bash
# Clean old data files (older than 30 days)
find /opt/esp32gateway/data -name "*.json" -mtime +30 -delete

# Archive old logs
tar -czf logs_$(date +%Y%m%d).tar.gz /var/log/esp32gateway/*.log.*
```

#### Configuration Updates

```bash
# Update configuration
vim .env

# Restart application (if running as service)
sudo systemctl restart esp32gateway

# Or restart manually
pkill -f gateway_manager
python deploy.py --mode gateway
```

### Backup and Recovery

#### Configuration Backup

```bash
# Backup configuration
cp .env .env.backup.$(date +%Y%m%d)

# Backup entire configuration
tar -czf esp32gateway_config_$(date +%Y%m%d).tar.gz .env config.py
```

#### Data Backup

```bash
# Backup data directory
tar -czf esp32gateway_data_$(date +%Y%m%d).tar.gz /opt/esp32gateway/data/

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backup/esp32gateway"
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "$BACKUP_DIR/esp32gateway_$DATE.tar.gz" /opt/esp32gateway/
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete
```

---

## Support

For additional support:

1. **Check the logs** for detailed error messages
2. **Review configuration** using the validation tools
3. **Test network connectivity** to all required services
4. **Consult the troubleshooting section** for common issues

For technical support, contact: sales@kkmcn.<NAME_EMAIL>
