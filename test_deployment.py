#!/usr/bin/env python3
"""
ESP32Gateway Deployment Test Script

This script tests the deployment without requiring dependencies to be installed.
It validates the file structure and basic functionality.
"""

import os
import sys
import json
from pathlib import Path


def test_file_structure():
    """Test that all required files are present."""
    print("Testing file structure...")
    
    required_files = [
        "config.py",
        "gateway_manager.py", 
        "deploy.py",
        "health_check.py",
        "requirements.txt",
        ".env.example",
        "README_DEPLOYMENT.md",
        "setup_environment.py",
        "deploy.bat",
        "Dockerfile",
        "docker-compose.yml",
        "esp32gateway.service",
        "examples/demo_configs.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"  ✓ {file_path}")
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required files present")
    return True


def test_requirements_file():
    """Test that requirements.txt is valid."""
    print("\nTesting requirements.txt...")
    
    try:
        with open("requirements.txt", "r") as f:
            lines = f.readlines()
        
        # Check for key dependencies
        required_deps = ["paho-mqtt", "requests", "python-dotenv"]
        found_deps = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith("#"):
                dep_name = line.split("==")[0].split(">=")[0].split("<=")[0]
                if dep_name in required_deps:
                    found_deps.append(dep_name)
                    print(f"  ✓ {line}")
        
        missing_deps = set(required_deps) - set(found_deps)
        if missing_deps:
            print(f"❌ Missing dependencies: {missing_deps}")
            return False
        
        print("✅ Requirements file is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error reading requirements.txt: {e}")
        return False


def test_env_example():
    """Test that .env.example contains required variables."""
    print("\nTesting .env.example...")
    
    try:
        with open(".env.example", "r") as f:
            content = f.read()
        
        required_vars = [
            "MQTT_BROKER",
            "MQTT_PORT", 
            "GATEWAY_MAC_ADDRESSES",
            "HTTP_GATEWAY_IP",
            "LOG_LEVEL"
        ]
        
        missing_vars = []
        for var in required_vars:
            if var not in content:
                missing_vars.append(var)
            else:
                print(f"  ✓ {var}")
        
        if missing_vars:
            print(f"❌ Missing environment variables: {missing_vars}")
            return False
        
        print("✅ Environment example file is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error reading .env.example: {e}")
        return False


def test_demo_configs():
    """Test that demo configurations are valid JSON."""
    print("\nTesting demo configurations...")
    
    try:
        with open("examples/demo_configs.json", "r") as f:
            configs = json.load(f)
        
        # Check for required sections
        required_sections = [
            "gateway_config_examples",
            "beacon_config_examples", 
            "http_config_examples",
            "deployment_scenarios"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in configs:
                missing_sections.append(section)
            else:
                print(f"  ✓ {section}")
        
        if missing_sections:
            print(f"❌ Missing configuration sections: {missing_sections}")
            return False
        
        print("✅ Demo configurations are valid")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in demo_configs.json: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading demo_configs.json: {e}")
        return False


def test_docker_files():
    """Test Docker configuration files."""
    print("\nTesting Docker files...")
    
    # Test Dockerfile
    try:
        with open("Dockerfile", "r") as f:
            dockerfile_content = f.read()
        
        if "FROM python:" in dockerfile_content and "EXPOSE" in dockerfile_content:
            print("  ✓ Dockerfile")
        else:
            print("  ❌ Dockerfile missing required content")
            return False
            
    except Exception as e:
        print(f"  ❌ Error reading Dockerfile: {e}")
        return False
    
    # Test docker-compose.yml
    try:
        with open("docker-compose.yml", "r") as f:
            compose_content = f.read()
        
        if "esp32gateway:" in compose_content and "ports:" in compose_content:
            print("  ✓ docker-compose.yml")
        else:
            print("  ❌ docker-compose.yml missing required content")
            return False
            
    except Exception as e:
        print(f"  ❌ Error reading docker-compose.yml: {e}")
        return False
    
    print("✅ Docker files are valid")
    return True


def test_python_syntax():
    """Test that Python files have valid syntax."""
    print("\nTesting Python file syntax...")
    
    python_files = [
        "config.py",
        "gateway_manager.py",
        "deploy.py", 
        "health_check.py",
        "setup_environment.py"
    ]
    
    for file_path in python_files:
        try:
            with open(file_path, "r") as f:
                content = f.read()
            
            # Try to compile the code
            compile(content, file_path, "exec")
            print(f"  ✓ {file_path}")
            
        except SyntaxError as e:
            print(f"  ❌ Syntax error in {file_path}: {e}")
            return False
        except Exception as e:
            print(f"  ❌ Error reading {file_path}: {e}")
            return False
    
    print("✅ All Python files have valid syntax")
    return True


def test_network_agnostic_design():
    """Test that hardcoded IPs have been removed."""
    print("\nTesting network-agnostic design...")
    
    # Check that original demo files still exist but new files don't have hardcoded IPs
    files_to_check = ["config.py", "gateway_manager.py", ".env.example"]
    
    hardcoded_patterns = [
        "*************",  # Original hardcoded IP
        "mqtt.kkmiot.com"  # Should only be in defaults, not hardcoded
    ]
    
    for file_path in files_to_check:
        try:
            with open(file_path, "r") as f:
                content = f.read()
            
            # For config.py and gateway_manager.py, these should not have hardcoded values
            if file_path in ["config.py", "gateway_manager.py"]:
                if "*************" in content:
                    print(f"  ❌ {file_path} still contains hardcoded IP")
                    return False
                else:
                    print(f"  ✓ {file_path} - no hardcoded IPs")
            
        except Exception as e:
            print(f"  ❌ Error reading {file_path}: {e}")
            return False
    
    print("✅ Network-agnostic design verified")
    return True


def main():
    """Run all deployment tests."""
    print("=" * 60)
    print(" ESP32Gateway Deployment Test Suite")
    print("=" * 60)
    
    tests = [
        test_file_structure,
        test_requirements_file,
        test_env_example,
        test_demo_configs,
        test_docker_files,
        test_python_syntax,
        test_network_agnostic_design
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f" Test Results: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("\n🎉 All tests passed! Your deployment is ready.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env")
        print("2. Edit .env with your configuration")
        print("3. Run: python deploy.py")
        return True
    else:
        print(f"\n❌ {failed} test(s) failed. Please fix the issues before deploying.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
